# This workflow is deprecated and replaced by:
# - test-release.yml (for TestPyPI on tags)
# - production-release.yml (for PyPI with manual trigger)
#
# Keeping this file for now to avoid breaking existing references
# TODO: Remove this file after confirming new workflows work

name: "[DEPRECATED] Old Release Workflow"

on:
  workflow_dispatch:  # Only manual trigger now
    inputs:
      confirm:
        description: 'This workflow is deprecated. Use test-release.yml or production-release.yml instead.'
        required: true
        default: 'I understand this is deprecated'

jobs:
  deprecated-notice:
    runs-on: ubuntu-latest
    steps:
    - name: Deprecation Notice
      run: |
        echo "⚠️  This workflow is deprecated!"
        echo ""
        echo "Please use instead:"
        echo "- test-release.yml: Publishes to TestPyPI on git tags"
        echo "- production-release.yml: Manual workflow for PyPI + GitHub releases"
        echo ""
        echo "Input provided: ${{ github.event.inputs.confirm }}"
        exit 1
