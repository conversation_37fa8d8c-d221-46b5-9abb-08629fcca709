name: Test Release to TestPyPI

on:
  push:
    tags:
      - 'v*'  # Trigger on version tags like v1.0.0, v2.1.3, etc.
      - '[0-9]+.*'  # Also trigger on CalVer tags like 25.5.28

jobs:
  build:
    name: Build distribution 📦
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.x"

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install build

    - name: Build distribution
      run: python -m build

    - name: Store the distribution packages
      uses: actions/upload-artifact@v4
      with:
        name: python-package-distributions
        path: dist/

  test-install:
    name: Test installation
    needs: build
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest]  # Focus on Linux for release testing
        python-version: ['3.9', '3.11']  # Test on min and stable versions

    steps:
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Download distribution packages
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/

    - name: Test wheel installation
      run: |
        python -m pip install --upgrade pip
        python -m pip install dist/*.whl
        funannotate2 --help

  publish-to-testpypi:
    name: Publish to TestPyPI 🧪
    needs: [build, test-install]
    runs-on: ubuntu-latest

    environment:
      name: testpypi
      url: https://test.pypi.org/p/funannotate2

    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing

    steps:
    - name: Download distribution packages
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/

    - name: Publish to TestPyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/
        verbose: true
        print-hash: true

    - name: Test TestPyPI installation
      run: |
        echo "Waiting for package to be available on TestPyPI..."
        sleep 120  # Wait longer for package to be available
        echo "Attempting to install from TestPyPI..."
        python -m pip install --index-url https://test.pypi.org/simple/ --extra-index-url https://pypi.org/simple/ funannotate2
        echo "Testing installation..."
        funannotate2 --help
