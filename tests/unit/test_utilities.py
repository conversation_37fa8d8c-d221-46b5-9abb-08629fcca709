#!/usr/bin/env python3

import os
import unittest
from unittest import mock
import tempfile
import shutil

from funannotate2_addons.utilities import (
    log_command,
    memorycheck,
    human_readable_size,
    which_path,
    internet_check,
    download_file,
)


class TestUtilities(unittest.TestCase):
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        # Remove the temporary directory
        shutil.rmtree(self.test_dir)
    
    @mock.patch("builtins.print")
    def test_log_command_list(self, mock_print):
        # Test logging a command as a list
        cmd = ["python", "-m", "pytest", "--verbose"]
        log_command(cmd)
        
        # Check that print was called with the formatted command
        mock_print.assert_called_once()
        call_args = mock_print.call_args[0][0]
        self.assertIn("[COMMAND]", call_args)
        self.assertIn("python", call_args)
        self.assertIn("pytest", call_args)
        self.assertIn("--verbose", call_args)
    
    @mock.patch("builtins.print")
    def test_log_command_string(self, mock_print):
        # Test logging a command as a string
        cmd = "python -m pytest --verbose"
        log_command(cmd)
        
        # Check that print was called with the command
        mock_print.assert_called_once()
        call_args = mock_print.call_args[0][0]
        self.assertIn("[COMMAND]", call_args)
        self.assertIn(cmd, call_args)
    
    @mock.patch("os.sysconf")
    def test_memorycheck(self, mock_sysconf):
        # Mock system configuration values
        mock_sysconf.side_effect = lambda x: {
            "SC_PAGE_SIZE": 4096,
            "SC_PHYS_PAGES": 2097152  # 8GB total
        }[x]
        
        # Test memory check
        memory_gib = memorycheck()
        self.assertAlmostEqual(memory_gib, 8.0, places=1)
    
    def test_human_readable_size(self):
        # Test various sizes
        self.assertEqual(human_readable_size(512), "512.00 B")
        self.assertEqual(human_readable_size(1024), "1.00 KiB")
        self.assertEqual(human_readable_size(1536), "1.50 KiB")
        self.assertEqual(human_readable_size(1048576), "1.00 MiB")
        self.assertEqual(human_readable_size(1073741824), "1.00 GiB")
        self.assertEqual(human_readable_size(1099511627776), "1.00 TiB")
        
        # Test with different decimal places
        self.assertEqual(human_readable_size(1536, decimal_places=1), "1.5 KiB")
        self.assertEqual(human_readable_size(1536, decimal_places=3), "1.500 KiB")
    
    @mock.patch("shutil.which")
    def test_which_path_found(self, mock_which):
        # Mock shutil.which to return a path
        mock_which.return_value = "/usr/bin/python"
        
        # Test finding an executable
        path = which_path("python")
        self.assertEqual(path, "/usr/bin/python")
        mock_which.assert_called_once_with("python")
    
    @mock.patch("shutil.which")
    def test_which_path_not_found(self, mock_which):
        # Mock shutil.which to return None
        mock_which.return_value = None
        
        # Test not finding an executable
        path = which_path("nonexistent_program")
        self.assertIsNone(path)
        mock_which.assert_called_once_with("nonexistent_program")
    
    @mock.patch("socket.create_connection")
    def test_internet_check_success(self, mock_connection):
        # Mock successful connection
        mock_connection.return_value = mock.Mock()
        
        # Test internet check
        result = internet_check()
        self.assertTrue(result)
        mock_connection.assert_called_once()
    
    @mock.patch("socket.create_connection")
    def test_internet_check_failure(self, mock_connection):
        # Mock connection failure
        mock_connection.side_effect = OSError("Connection failed")
        
        # Test internet check failure
        result = internet_check()
        self.assertFalse(result)
        mock_connection.assert_called_once()
    
    @mock.patch("urllib.request.urlopen")
    def test_download_file_success(self, mock_urlopen):
        # Mock successful download
        mock_response = mock.Mock()
        mock_response.read.return_value = b"test file content"
        mock_urlopen.return_value.__enter__.return_value = mock_response
        
        # Test file download
        test_file = os.path.join(self.test_dir, "test_download.txt")
        url = "https://example.com/test.txt"
        
        result = download_file(url, test_file)
        self.assertTrue(result)
        
        # Check that file was created with correct content
        self.assertTrue(os.path.exists(test_file))
        with open(test_file, "rb") as f:
            content = f.read()
        self.assertEqual(content, b"test file content")
    
    @mock.patch("urllib.request.urlopen")
    def test_download_file_failure(self, mock_urlopen):
        # Mock download failure
        mock_urlopen.side_effect = Exception("Download failed")
        
        # Test file download failure
        test_file = os.path.join(self.test_dir, "test_download.txt")
        url = "https://example.com/test.txt"
        
        result = download_file(url, test_file)
        self.assertFalse(result)
        
        # Check that file was not created
        self.assertFalse(os.path.exists(test_file))


if __name__ == "__main__":
    unittest.main()
